#pragma once

#include "stream_link.h"
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>

namespace SPipeline {

/**
 * AsyncBridge - A thread-safe link that buffers data for cross-thread communication
 * with configurable buffer size.
 * 
 * This link provides thread-safe buffering for scenarios where data needs to be passed
 * between different threads. It uses a queue with configurable maximum size and blocks
 * when the buffer is full.
 */
template<typename TData>
class AsyncBridge : public StreamLink<TData, TData> {
public:
  using StreamLink<TData, TData>::outputCallback_;

  /**
  * Constructor
  * @param maxBufferSize Maximum number of items to buffer
  * @param tickTimeout Timeout for tick() method blocking (default: 100ms)
  */
  explicit AsyncBridge(const size_t maxBufferSize, const std::chrono::milliseconds tickTimeout = std::chrono::milliseconds(100))
    : tickTimeout_(tickTimeout), maxBufferSize_(maxBufferSize) {
    this->setRunning();  // AsyncBridge starts in running state
  }

  /**
  * Destructor - ensures proper shutdown
  */
  ~AsyncBridge() override {
    stop();
  }

  /**
  * IStreamLinkInput<TData> implementation
  * Adds data to the buffer if space is available and not shutting down.
  * @param data Data to forward
  * @return true if data was successfully added, false if buffer full or shutting down
  */
  bool forward(TData& data) override {
    std::lock_guard lock(bufferMutex_);

    // Check if we have space and are not shutting down
    auto bufferSize = buffer_.size();
    if (bufferSize >= maxBufferSize_ || !this->running()) {
      return false;
    }

    buffer_.push(data);
    dataAvailable_.notify_one();
    return true;
  }

  /**
  * IStreamLinkOutput<TData> implementation
  * Registers the callback function that will receive processed data.
  */
  void setOutputCallback(typename IStreamLinkOutput<TData>::OutputCallback callback) override {
    std::lock_guard lock(bufferMutex_);
    outputCallback_ = std::move(callback);
  }

  /**
  * Tickable implementation
  * Blocks until data is available or timeout expires, then processes all buffered data.
  * @return true to continue ticking, false to stop (shutdown or callback failure)
  */
  bool tick() override {
    std::unique_lock lock(bufferMutex_);

    // Block until data is available or timeout expires
    auto dataReceived = dataAvailable_.wait_for(lock, tickTimeout_, [this] {
      return !this->buffer_.empty() || !this->running();
    });

    if (!this->running()) {
      return false; // If shutting down, return false to stop ticking
    }

    if (!dataReceived) {
      return true; // If timeout expired without data available, just return true to continue ticking
    }

    // Process all available data
    while (!buffer_.empty()) {
      TData data = buffer_.front();
      buffer_.pop();

      // Unlock before calling callback to prevent deadlock
      lock.unlock();

      // Call output callback and check return value
      auto continueProcessing = outputCallback_(data);
      if (!continueProcessing) {
        // Callback signaled to stop - initiate shutdown and return false
        stop();
        return false;
      }
      lock.lock();
    }

    return this->running();  // Continue ticking unless shutdown
  }

  /**
  * Tickable implementation
  * Returns true if there is buffered data waiting to be processed.
  */
  bool hasPendingWork() const override {
    std::lock_guard lock(bufferMutex_);
    return !buffer_.empty();
  }

  /**
  * Get the current buffer size
  * @return Number of items currently in the buffer
  */
  size_t size() const {
    std::lock_guard lock(bufferMutex_);
    return buffer_.size();
  }

  /**
  * Initiates shutdown of the bridge.
  * Wakes up any threads waiting on the buffer and prevents new data from being added.
  */
  void stop() override {
    PipelineComponent::stop();  // Call base class shutdown to set running_ = false
    dataAvailable_.notify_all();    // Wake up any threads waiting on the buffer
  }

private:
  std::queue<TData> buffer_;
  mutable std::mutex bufferMutex_;
  std::condition_variable dataAvailable_;
  std::chrono::milliseconds tickTimeout_;
  size_t maxBufferSize_;
};

} // namespace SPipeline
