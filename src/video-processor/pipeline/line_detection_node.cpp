#include "./line_detection_node.h"

namespace IQVideoProcessor::Pipeline {

LineDetectionNode::LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples)
  : sampleRate_(sampleRate),
    maxVideoLineSamples_(maxVideoLineSamples)
  {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& inSegment) {
  if (!running()) return false;

  return running();
}

} // namespace IQVideoProcessor::Pipeline
