#pragma once

#include "./iq_demodulation_node_types.h"
#include "../../stream-pipeline/stream_node.h"

namespace IQVideoProcessor::Pipeline {

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, DemodulatedSegment> {
public:
  LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& inSegment) override;

  SampleRateType sampleRate_;
  size_t maxVideoLineSamples_;
};

} // namespace IQVideoProcessor::Pipeline
